"use client";

import React from "react";
import { HeroBanner } from "./HeroBanner";
import { BannerType, type BannerProps } from "./types";

// Example banner data
export const exampleBanners: BannerProps[] = [
  // Left-Right Text Banner
  {
    id: "welcome-banner",
    type: BannerType.LEFT_RIGHT_TEXT,
    title: "Find Your Perfect Home",
    subtitle: "Premium Rentals",
    description: "Discover luxury apartments and homes in prime locations. Our curated selection offers the best in comfort, style, and convenience for your next home.",
    imageUrl: "/images/modern-apartment.jpg",
    imageAlt: "Modern luxury apartment interior",
    layout: "text-left",
    ctaButton: {
      text: "Browse Properties",
      href: "/properties",
      variant: "default"
    },
    backgroundColor: "bg-gradient-to-r from-primary-50 to-secondary-50"
  },

  // Text-CTA Banner
  {
    id: "cta-banner",
    type: BannerType.TEXT_CTA,
    title: "Ready to Move In?",
    subtitle: "Special Offer",
    description: "Get your first month free on selected premium properties. Limited time offer for new tenants.",
    primaryCTA: {
      text: "View Offers",
      href: "/offers",
      variant: "default"
    },
    secondaryCTA: {
      text: "Learn More",
      href: "/about",
      variant: "outline"
    },
    backgroundColor: "bg-gradient-to-br from-primary-700 via-primary-800 to-primary-900"
  },

  // Image Overlay Banner
  {
    id: "lifestyle-banner",
    type: BannerType.IMAGE_OVERLAY,
    title: "Live the Lifestyle You Deserve",
    subtitle: "Premium Living",
    description: "Experience luxury living with world-class amenities, stunning views, and unmatched convenience.",
    backgroundImage: "/images/luxury-building.jpg",
    overlayOpacity: 0.4,
    textPosition: "center",
    ctaButton: {
      text: "Explore Amenities",
      href: "/amenities",
      variant: "default"
    }
  },

  // Property Showcase Banner
  {
    id: "featured-property",
    type: BannerType.PROPERTY_SHOWCASE,
    property: {
      id: "prop-001",
      title: "Skyline Penthouse",
      address: "Downtown Manhattan, New York",
      price: 4500,
      priceUnit: "month",
      bedrooms: 3,
      bathrooms: 2,
      area: 1200,
      areaUnit: "sqft",
      images: [
        "/images/penthouse-main.jpg",
        "/images/penthouse-living.jpg",
        "/images/penthouse-bedroom.jpg"
      ],
      features: [
        "City Views",
        "Modern Kitchen",
        "Hardwood Floors",
        "In-unit Laundry",
        "Balcony",
        "Parking Included"
      ]
    },
    ctaButton: {
      text: "Schedule Tour",
      href: "/properties/prop-001",
      variant: "default"
    },
    showFeatures: true
  }
];

// Example usage component
export const BannerExamples: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Full Hero Banner with all examples */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Complete Hero Banner System</h2>
        <HeroBanner
          banners={exampleBanners}
          autoPlay={true}
          autoPlayInterval={6000}
          showNavigation={true}
          showDots={true}
        />
      </div>

      {/* Individual banner examples */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Individual Banner Examples</h2>
        
        {exampleBanners.map((banner) => (
          <div key={banner.id} className="border rounded-lg overflow-hidden">
            <div className="bg-gray-100 px-4 py-2">
              <h3 className="font-semibold capitalize">
                {banner.type.replace('-', ' ')} Banner
              </h3>
            </div>
            <HeroBanner
              banners={[banner]}
              autoPlay={false}
              showNavigation={false}
              showDots={false}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
