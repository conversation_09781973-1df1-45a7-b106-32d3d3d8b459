"use client";

import React, { useState, useEffect, useCallback } from "react";
import { NAVBAR_HEIGHT } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { type HeroBannerProps, type BannerProps, BannerType } from "./types";
import { BannerNavigation } from "./BannerNavigation";
import { LeftRightTextBanner } from "./LeftRightTextBanner";
import { TextCTABanner } from "./TextCTABanner";
import { ImageOverlayBanner } from "./ImageOverlayBanner";
import { PropertyShowcaseBanner } from "./PropertyShowcaseBanner";

export const HeroBanner: React.FC<HeroBannerProps> = ({
  banners,
  autoPlay = true,
  autoPlayInterval = 5000,
  showNavigation = true,
  showDots = true,
  className,
  children,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || banners.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === banners.length - 1 ? 0 : prevIndex + 1
      );
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isAutoPlaying, banners.length, autoPlayInterval]);

  // Navigation handlers
  const handlePrevious = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? banners.length - 1 : prevIndex - 1
    );
    setIsAutoPlaying(false);
  }, [banners.length]);

  const handleNext = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === banners.length - 1 ? 0 : prevIndex + 1
    );
    setIsAutoPlaying(false);
  }, [banners.length]);

  const handleDotClick = useCallback((index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  }, []);

  // Resume auto-play after user interaction
  useEffect(() => {
    if (!autoPlay) return;

    const timer = setTimeout(() => {
      setIsAutoPlaying(true);
    }, autoPlayInterval * 2);

    return () => clearTimeout(timer);
  }, [currentIndex, autoPlay, autoPlayInterval]);

  // Render individual banner based on type
  const renderBanner = (banner: BannerProps, index: number) => {
    const isActive = index === currentIndex;
    const bannerProps = { ...banner, isActive };

    switch (banner.type) {
      case BannerType.LEFT_RIGHT_TEXT:
        return <LeftRightTextBanner key={banner.id} {...bannerProps} />;
      case BannerType.TEXT_CTA:
        return <TextCTABanner key={banner.id} {...bannerProps} />;
      case BannerType.IMAGE_OVERLAY:
        return <ImageOverlayBanner key={banner.id} {...bannerProps} />;
      case BannerType.PROPERTY_SHOWCASE:
        return <PropertyShowcaseBanner key={banner.id} {...bannerProps} />;
      default:
        return null;
    }
  };

  if (!banners || banners.length === 0) {
    return null;
  }

  return (
    <section
      className={cn(
        "relative w-full overflow-hidden",
        className
      )}
      style={{
        marginTop: `${NAVBAR_HEIGHT}px`,
        minHeight: "400px"
      }}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => autoPlay && setIsAutoPlaying(true)}
    >
      {/* Banner Container */}
      <div
        className="flex transition-transform duration-500 ease-in-out h-full"
        style={{
          transform: `translateX(-${currentIndex * 100}%)`,
          width: `${banners.length * 100}%`
        }}
      >
        {banners.map((banner, index) => (
          <div
            key={banner.id}
            className="w-full flex-shrink-0 h-full"
            style={{ width: `${100 / banners.length}%` }}
          >
            {renderBanner(banner, index)}
          </div>
        ))}
      </div>

      {/* Navigation */}
      {showNavigation && banners.length > 1 && (
        <BannerNavigation
          currentIndex={currentIndex}
          totalBanners={banners.length}
          onPrevious={handlePrevious}
          onNext={handleNext}
          onDotClick={handleDotClick}
          showDots={showDots}
          showArrows={true}
        />
      )}

      {/* Custom children content */}
      {children}
    </section>
  );
};