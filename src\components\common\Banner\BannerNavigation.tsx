"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type BannerNavigationProps } from "./types";

export const BannerNavigation: React.FC<BannerNavigationProps> = ({
  currentIndex,
  totalBanners,
  onPrevious,
  onNext,
  onDotClick,
  showDots = true,
  showArrows = true,
}) => {
  return (
    <>
      {/* Arrow Navigation */}
      {showArrows && (
        <>
          {/* Previous Button */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white border-white/20 shadow-lg backdrop-blur-sm"
            onClick={onPrevious}
            aria-label="Previous banner"
          >
            <ChevronLeft className="h-4 w-4 text-primary-700" />
          </Button>

          {/* Next Button */}
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white border-white/20 shadow-lg backdrop-blur-sm"
            onClick={onNext}
            aria-label="Next banner"
          >
            <ChevronRight className="h-4 w-4 text-primary-700" />
          </Button>
        </>
      )}

      {/* Dot Navigation */}
      {showDots && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-10">
          <div className="flex items-center gap-2 bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">
            {Array.from({ length: totalBanners }, (_, index) => (
              <button
                key={index}
                className={cn(
                  "w-2 h-2 rounded-full transition-all duration-300 hover:scale-125",
                  index === currentIndex
                    ? "bg-white scale-125"
                    : "bg-white/50 hover:bg-white/75"
                )}
                onClick={() => onDotClick(index)}
                aria-label={`Go to banner ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};
