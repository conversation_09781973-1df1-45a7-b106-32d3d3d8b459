import "../styles/globals.css";

import { type <PERSON>adata } from "next";
import { <PERSON>ei<PERSON> } from "next/font/google";

export const metadata: Metadata = {
  title: "Home | Next.js",
  description: "Generated by create next app",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`}>
      <body>{children}</body>
    </html>
  );
}
