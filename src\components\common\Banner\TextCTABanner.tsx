"use client";

import React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type TextCTABannerProps } from "./types";

export const TextCTABanner: React.FC<TextCTABannerProps> = ({
  title,
  subtitle,
  description,
  primaryCTA,
  secondaryCTA,
  backgroundColor = "bg-gradient-to-br from-primary-700 via-primary-800 to-primary-900",
  backgroundImage,
  isActive,
}) => {
  return (
    <div 
      className={cn(
        "w-full h-full min-h-[400px] lg:min-h-[500px] flex items-center justify-center relative",
        backgroundColor
      )}
      style={backgroundImage ? {
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      } : undefined}
    >
      {/* Background overlay */}
      {backgroundImage && (
        <div className="absolute inset-0 bg-black/50" />
      )}
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-4xl mx-auto space-y-8">
          {/* Subtitle */}
          {subtitle && (
            <div className="inline-block">
              <span className={cn(
                "text-sm font-medium px-4 py-2 rounded-full",
                backgroundImage || backgroundColor.includes('primary-7') 
                  ? "text-white bg-white/20 backdrop-blur-sm" 
                  : "text-primary-600 bg-primary-100"
              )}>
                {subtitle}
              </span>
            </div>
          )}
          
          {/* Main Title */}
          <h1 className={cn(
            "text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight",
            backgroundImage || backgroundColor.includes('primary-7')
              ? "text-white"
              : "text-primary-900"
          )}>
            {title}
          </h1>
          
          {/* Description */}
          {description && (
            <p className={cn(
              "text-xl leading-relaxed max-w-3xl mx-auto",
              backgroundImage || backgroundColor.includes('primary-7')
                ? "text-white/90"
                : "text-primary-700"
            )}>
              {description}
            </p>
          )}
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6">
            <Button
              asChild
              variant={primaryCTA.variant || "default"}
              size="lg"
              className={cn(
                "px-8 py-3 text-base font-medium min-w-[200px]",
                backgroundImage || backgroundColor.includes('primary-7')
                  ? "bg-white text-primary-900 hover:bg-white/90"
                  : "bg-primary-700 hover:bg-primary-800 text-white"
              )}
            >
              <Link href={primaryCTA.href}>
                {primaryCTA.text}
              </Link>
            </Button>
            
            {secondaryCTA && (
              <Button
                asChild
                variant={secondaryCTA.variant || "outline"}
                size="lg"
                className={cn(
                  "px-8 py-3 text-base font-medium min-w-[200px]",
                  backgroundImage || backgroundColor.includes('primary-7')
                    ? "border-white text-white hover:bg-white hover:text-primary-900"
                    : "border-primary-700 text-primary-700 hover:bg-primary-700 hover:text-white"
                )}
              >
                <Link href={secondaryCTA.href}>
                  {secondaryCTA.text}
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl" />
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-xl" />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-secondary-400/20 rounded-full blur-lg" />
    </div>
  );
};
