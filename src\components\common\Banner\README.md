# Hero Banner System

A comprehensive, modular hero banner system for the real estate PWA website. This system provides a flexible carousel-based banner solution with multiple banner types and smooth transitions.

## Features

- 🎠 **Carousel functionality** with smooth transitions
- 🎯 **4 different banner types** for various use cases
- 📱 **Fully responsive** design (mobile, tablet, desktop)
- ⚡ **Auto-play** with pause on hover
- 🎮 **Navigation controls** (arrows and dots)
- 🎨 **Consistent design system** integration
- 📍 **Navbar-aware positioning**
- 🔧 **TypeScript support** with full type safety

## Banner Types

### 1. Left-Right Text Banner
Perfect for introducing features or services with supporting imagery.
- Text content on one side, image on the other
- Configurable layout (text-left or text-right)
- Optional CTA button
- Customizable background

### 2. Text-CTA Banner
Ideal for promotional content and call-to-action focused messaging.
- Centered text layout
- Primary and secondary CTA buttons
- Background image support
- Gradient overlays

### 3. Image Overlay Banner
Great for lifestyle and atmospheric content.
- Full background image
- Configurable text position (left, center, right)
- Adjustable overlay opacity
- Single CTA button

### 4. Property Showcase Banner
Specifically designed for featuring properties.
- Property image gallery
- Property details (price, beds, baths, area)
- Feature highlights
- Direct property CTA

## Installation & Usage

### Basic Usage

```tsx
import { HeroBanner, BannerType } from "@/components/common/Banner";

const banners = [
  {
    id: "banner-1",
    type: BannerType.LEFT_RIGHT_TEXT,
    title: "Find Your Perfect Home",
    description: "Discover amazing properties...",
    imageUrl: "/images/home.jpg",
    imageAlt: "Beautiful home",
    layout: "text-left",
    ctaButton: {
      text: "Browse Properties",
      href: "/properties"
    }
  }
];

export default function HomePage() {
  return (
    <HeroBanner
      banners={banners}
      autoPlay={true}
      autoPlayInterval={5000}
      showNavigation={true}
      showDots={true}
    />
  );
}
```

### Advanced Configuration

```tsx
<HeroBanner
  banners={banners}
  autoPlay={false}                    // Disable auto-play
  autoPlayInterval={8000}             // 8 second intervals
  showNavigation={true}               // Show arrow navigation
  showDots={false}                    // Hide dot navigation
  className="custom-banner-class"     // Custom CSS classes
>
  {/* Custom overlay content */}
  <div className="absolute top-4 right-4 z-20">
    <CustomNotification />
  </div>
</HeroBanner>
```

## Banner Configuration Examples

### Left-Right Text Banner
```tsx
{
  id: "welcome",
  type: BannerType.LEFT_RIGHT_TEXT,
  title: "Welcome to Premium Living",
  subtitle: "New Properties",
  description: "Discover our latest collection...",
  imageUrl: "/images/apartment.jpg",
  imageAlt: "Modern apartment",
  layout: "text-left", // or "text-right"
  ctaButton: {
    text: "Explore Now",
    href: "/properties",
    variant: "default" // or "outline", "secondary"
  },
  backgroundColor: "bg-gradient-to-r from-primary-50 to-secondary-50"
}
```

### Text-CTA Banner
```tsx
{
  id: "promotion",
  type: BannerType.TEXT_CTA,
  title: "Limited Time Offer",
  subtitle: "Special Deal",
  description: "Get your first month free...",
  primaryCTA: {
    text: "Claim Offer",
    href: "/offers"
  },
  secondaryCTA: {
    text: "Learn More",
    href: "/about",
    variant: "outline"
  },
  backgroundImage: "/images/city-skyline.jpg"
}
```

### Image Overlay Banner
```tsx
{
  id: "lifestyle",
  type: BannerType.IMAGE_OVERLAY,
  title: "Live Your Best Life",
  description: "Experience luxury amenities...",
  backgroundImage: "/images/pool-area.jpg",
  overlayOpacity: 0.4,
  textPosition: "center", // or "left", "right"
  ctaButton: {
    text: "View Amenities",
    href: "/amenities"
  }
}
```

### Property Showcase Banner
```tsx
{
  id: "featured-property",
  type: BannerType.PROPERTY_SHOWCASE,
  property: {
    id: "prop-123",
    title: "Downtown Penthouse",
    address: "123 Main St, City Center",
    price: 3500,
    priceUnit: "month",
    bedrooms: 2,
    bathrooms: 2,
    area: 1100,
    areaUnit: "sqft",
    images: ["/images/prop1.jpg", "/images/prop2.jpg"],
    features: ["City Views", "Modern Kitchen", "Parking"]
  },
  ctaButton: {
    text: "Schedule Tour",
    href: "/properties/prop-123"
  },
  showFeatures: true
}
```

## Responsive Behavior

- **Mobile (< 768px)**: Single column layout, optimized touch navigation
- **Tablet (768px - 1024px)**: Balanced two-column layout where applicable
- **Desktop (> 1024px)**: Full layout with all features visible

## Accessibility Features

- Proper ARIA labels for navigation controls
- Keyboard navigation support
- Screen reader friendly content structure
- High contrast navigation elements

## Customization

### Styling
The banner system uses the existing design system colors and can be customized through:
- `backgroundColor` props for individual banners
- `className` prop for the main container
- CSS custom properties for fine-tuning

### Adding New Banner Types

1. Add new type to `BannerType` enum in `types.ts`
2. Create interface extending `BaseBannerProps`
3. Add to `BannerProps` union type
4. Create new banner component file
5. Add case to `renderBanner` function in `HeroBanner.tsx`
6. Export from `index.ts`

## Performance Considerations

- Images use Next.js `Image` component with proper optimization
- `priority` loading for active banners
- Responsive image sizing with `sizes` attribute
- Smooth CSS transitions instead of JavaScript animations
- Auto-play pauses on user interaction to save resources

## File Structure

```
src/components/common/Banner/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript interfaces
├── HeroBanner.tsx             # Main wrapper component
├── BannerNavigation.tsx       # Navigation controls
├── LeftRightTextBanner.tsx    # Left-right layout banner
├── TextCTABanner.tsx          # CTA-focused banner
├── ImageOverlayBanner.tsx     # Image overlay banner
├── PropertyShowcaseBanner.tsx # Property showcase banner
├── BannerExamples.tsx         # Usage examples
└── README.md                  # This documentation
```
