@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Primary color */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);
  --color-primary-950: var(--primary-950);

  /* Secondary color */
  --color-secondary-50: var(--secondary-50);
  --color-secondary-100: var(--secondary-100);
  --color-secondary-200: var(--secondary-200);
  --color-secondary-300: var(--secondary-300);
  --color-secondary-400: var(--secondary-400);
  --color-secondary-500: var(--secondary-500);
  --color-secondary-600: var(--secondary-600);
  --color-secondary-700: var(--secondary-700);
  --color-secondary-800: var(--secondary-800);
  --color-secondary-900: var(--secondary-900);
  --color-secondary-950: var(--secondary-950);

  /* Main color */
  --color-main-50: var(--main-50);
  --color-main-100: var(--main-100);
  --color-main-200: var(--main-200);
  --color-main-300: var(--main-300);
  --color-main-400: var(--main-400);
  --color-main-500: var(--main-500);
  --color-main-600: var(--main-600);
  --color-main-700: var(--main-700);
  --color-main-800: var(--main-800);
  --color-main-900: var(--main-900);
  --color-main-950: var(--main-950);
}

:root {
  --radius: 1rem;
  /* Pastel Light Theme */
  --background: oklch(0.98 0.01 85);
  --foreground: oklch(0.3 0.02 265);
  --card: oklch(0.97 0.02 95);
  --card-foreground: oklch(0.3 0.02 265);
  --popover: oklch(0.97 0.02 95);
  --popover-foreground: oklch(0.3 0.02 265);
  --primary: #c7c7cc;
  --primary-foreground: oklch(0.98 0.01 85);
  --primary-background: oklch(0.98 0.01 85);
  --primary-100: #f1f1f2;
  --primary-200: #e0e0e2;
  --primary-300: #c7c7cc;
  --primary-400: #a8a8af;
  --primary-500: #82828b;
  --primary-600: #57575f;
  --primary-700: #27272a;
  --primary-800: #111113;
  --primary-900: #040405;
  --primary-950: #000000;
  --main: oklch(0.7 0.12 260);
  --main-50: oklch(0.98 0.02 260);
  --main-100: oklch(0.95 0.03 260);
  --main-200: oklch(0.9 0.05 260);
  --main-300: oklch(0.85 0.08 260);
  --main-400: oklch(0.8 0.1 260);
  --main-500: oklch(0.7 0.12 260);
  --main-600: oklch(0.6 0.14 260);
  --main-700: oklch(0.5 0.15 260);
  --main-800: oklch(0.4 0.12 260);
  --main-900: oklch(0.3 0.1 260);
  --main-950: oklch(0.2 0.08 260);
  --secondary: oklch(0.9 0.05 180);
  --secondary-50: oklch(0.98 0.02 180);
  --secondary-100: oklch(0.95 0.03 180);
  --secondary-200: oklch(0.92 0.04 180);
  --secondary-300: oklch(0.9 0.05 180);
  --secondary-400: oklch(0.85 0.07 180);
  --secondary-500: oklch(0.8 0.09 180);
  --secondary-600: oklch(0.7 0.1 180);
  --secondary-700: oklch(0.6 0.12 180);
  --secondary-800: oklch(0.5 0.1 180);
  --secondary-900: oklch(0.35 0.08 180);
  --secondary-950: oklch(0.2 0.05 180);
  --secondary-foreground: oklch(0.3 0.02 265);
  --secondary-background: oklch(0.98 0.01 85);
  --muted: oklch(0.95 0.03 200);
  --muted-foreground: oklch(0.45 0.05 265);
  --accent: oklch(0.85 0.08 130);
  --accent-foreground: oklch(0.3 0.02 265);
  --destructive: oklch(0.75 0.15 25);
  --border: oklch(0.85 0.03 230);
  --input: oklch(0.85 0.03 230);
  --ring: oklch(0.7 0.12 260 / 30%);
  /* Pastel Chart Colors */
  --chart-1: oklch(0.75 0.12 260);
  /* Lavender */
  --chart-2: oklch(0.8 0.12 130);
  /* Mint */
  --chart-3: oklch(0.85 0.1 60);
  /* Peach */
  --chart-4: oklch(0.75 0.13 190);
  /* Sky blue */
  --chart-5: oklch(0.8 0.1 330);
  /* Rose */
  /* Sidebar Colors */
  --sidebar: oklch(0.97 0.02 240);
  --sidebar-foreground: oklch(0.3 0.02 265);
  --sidebar-primary: oklch(0.7 0.12 260);
  --sidebar-primary-foreground: oklch(0.98 0.01 85);
  --sidebar-accent: oklch(0.9 0.05 180);
  --sidebar-accent-foreground: oklch(0.3 0.02 265);
  --sidebar-border: oklch(0.85 0.03 230);
  --sidebar-ring: oklch(0.7 0.12 260 / 30%);
}

.dark {
  /* Pastel Dark Theme */
  --background: oklch(0.2 0.02 265);
  --foreground: oklch(0.9 0.02 85);
  --card: oklch(0.25 0.03 270);
  --card-foreground: oklch(0.9 0.02 85);
  --popover: oklch(0.25 0.03 270);
  --popover-foreground: oklch(0.9 0.02 85);
  --primary: oklch(0.3 0.05 270);
  --primary-foreground: oklch(0.2 0.02 265);
  --primary-background: oklch(0.2 0.02 265);
  --main: oklch(0.7 0.15 260);
  --secondary: oklch(0.35 0.08 270);
  --secondary-foreground: oklch(0.9 0.02 85);
  --secondary-background: oklch(0.2 0.02 265);
  --main: oklch(0.65 0.15 260);
  --main-50: oklch(0.95 0.03 260);
  --main-100: oklch(0.9 0.05 260);
  --main-200: oklch(0.85 0.08 260);
  --main-300: oklch(0.8 0.1 260);
  --main-400: oklch(0.75 0.12 260);
  --main-500: oklch(0.65 0.15 260);
  --main-600: oklch(0.55 0.17 260);
  --main-700: oklch(0.45 0.15 260);
  --main-800: oklch(0.35 0.12 260);
  --main-900: oklch(0.25 0.1 260);
  --main-950: oklch(0.15 0.08 260);
  --muted: oklch(0.3 0.05 270);
  --muted-foreground: oklch(0.7 0.05 275);
  --accent: oklch(0.6 0.12 130);
  --accent-foreground: oklch(0.2 0.02 265);
  --destructive: oklch(0.65 0.2 25);
  --border: oklch(0.9 0.02 85 / 15%);
  --input: oklch(0.9 0.02 85 / 20%);
  --ring: oklch(0.7 0.15 260 / 30%);
  /* Pastel Chart Colors for Dark Theme */
  --chart-1: oklch(0.75 0.18 260);
  /* Bright lavender */
  --chart-2: oklch(0.7 0.15 130);
  /* Bright mint */
  --chart-3: oklch(0.75 0.15 60);
  /* Bright peach */
  --chart-4: oklch(0.7 0.17 190);
  /* Bright sky blue */
  --chart-5: oklch(0.75 0.15 330);
  /* Bright rose */
  /* Sidebar Colors */
  --sidebar: oklch(0.25 0.03 270);
  --sidebar-foreground: oklch(0.9 0.02 85);
  --sidebar-primary: oklch(0.65 0.18 260);
  --sidebar-primary-foreground: oklch(0.98 0.01 85);
  --sidebar-accent: oklch(0.35 0.08 270);
  --sidebar-accent-foreground: oklch(0.9 0.02 85);
  --sidebar-border: oklch(0.9 0.02 85 / 15%);
  --sidebar-ring: oklch(0.7 0.15 260 / 30%);
}


.dashboard-container {
  @apply pt-8 pb-5 px-8;
}

.mapboxgl-popup-content {
  @apply !bg-primary-700;
  @apply !rounded-lg;
  @apply !py-2;
  @apply !px-3;
}

.mapboxgl-popup-anchor-top .mapboxgl-popup-tip {
  @apply !border-b-primary-700;
}

.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
  @apply !border-t-primary-700;
}

.mapboxgl-popup-anchor-left .mapboxgl-popup-tip {
  @apply !border-r-primary-700;
}

.mapboxgl-popup-anchor-right .mapboxgl-popup-tip {
  @apply !border-l-primary-700;
}

.marker-popup {
  @apply bg-primary-700;
  @apply text-white;
  @apply p-0;
  @apply m-0;
  @apply !flex;
  @apply justify-between;
  @apply items-center;
  @apply gap-3;
}

.marker-popup-image {
  @apply w-10 h-10 object-cover bg-white rounded-lg;
}

.marker-popup-price {
  @apply text-sm font-semibold text-primary-200;
}

.marker-popup-title {
  @apply hover:underline hover:text-blue-300 cursor-pointer;
  @apply focus:outline-none;
}

.marker-popup-price-unit {
  @apply text-xs text-primary-500 font-normal;
}

/* scrollbar styling */
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-200;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-300;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Amplify UI Overrides */
[data-amplify-authenticator] {
  --amplify-components-button-primary-background-color: var(--primary);
  --amplify-components-button-primary-hover-background-color: hsl(var(--primary) / 0.9);
  --amplify-components-button-border-radius: var(--radius);
  --amplify-components-fieldcontrol-border-radius: var(--radius);
}

[data-amplify-authenticator][data-variation="default"] {
  height: 100%;
  padding: 2rem !important;
}

[data-amplify-authenticator] [data-amplify-router] {
  border: none !important;
  box-shadow: none !important;
  max-width: 400px !important;
  margin: 0 auto;
}

[data-amplify-authenticator] [data-amplify-container] {
  border-radius: var(--radius);
  padding: 2rem !important;
  border: 1px solid hsl(var(--border));
}

[data-amplify-authenticator] [data-amplify-form] {
  padding: 0 !important;
}

[data-amplify-authenticator] .amplify-tabs__list {
  display: none;
}

[data-amplify-authenticator] .amplify-button--primary {
  width: 100%;
  height: 2.75rem;
  margin-top: 1rem;
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  @apply font-medium;
  @apply text-sm;
}

[data-amplify-authenticator] .amplify-button--primary:hover {
  background-color: hsl(var(--primary) / 0.8) !important;
}

[data-amplify-authenticator] .amplify-field-group__control {
  border-color: hsl(var(--input));
}

[data-amplify-authenticator] .amplify-field-group__control:focus-within {
  border-color: hsl(var(--ring)) !important;
  box-shadow: 0 0 0 1px hsl(var(--ring)) !important;
}

[data-amplify-authenticator] .amplify-field__show-password {
  color: hsl(var(--muted-foreground));
}

[data-amplify-authenticator] .amplify-label {
  @apply text-sm font-medium;
  color: hsl(var(--foreground));
}

[data-amplify-authenticator] .amplify-select {
  border-color: hsl(var(--input));
  border-radius: var(--radius);
  height: 2.5rem;
  @apply text-sm;
}

[data-amplify-authenticator] .amplify-text--error {
  color: hsl(var(--destructive));
}

/* Sonner Toast Styles */
[data-close-button="true"] {
  @apply bg-background border-border text-foreground hover:bg-muted;
}