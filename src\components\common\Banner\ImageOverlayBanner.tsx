"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type ImageOverlayBannerProps } from "./types";

export const ImageOverlayBanner: React.FC<ImageOverlayBannerProps> = ({
  title,
  subtitle,
  description,
  backgroundImage,
  overlayOpacity = 0.5,
  textPosition = "center",
  ctaButton,
  isActive,
}) => {
  const getTextAlignment = () => {
    switch (textPosition) {
      case "left":
        return "text-left items-start";
      case "right":
        return "text-right items-end";
      default:
        return "text-center items-center";
    }
  };

  const getContentAlignment = () => {
    switch (textPosition) {
      case "left":
        return "justify-start";
      case "right":
        return "justify-end";
      default:
        return "justify-center";
    }
  };

  return (
    <div className="w-full h-full min-h-[400px] lg:min-h-[500px] relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src={backgroundImage}
          alt="Banner background"
          fill
          className="object-cover transition-transform duration-700 hover:scale-105"
          priority={isActive}
          sizes="100vw"
        />
      </div>
      
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black transition-opacity duration-300"
        style={{ opacity: overlayOpacity }}
      />
      
      {/* Content */}
      <div className={cn(
        "relative z-10 h-full flex items-center",
        getContentAlignment()
      )}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className={cn(
            "max-w-4xl space-y-6 flex flex-col",
            getTextAlignment(),
            textPosition === "left" && "lg:max-w-2xl",
            textPosition === "right" && "lg:max-w-2xl lg:ml-auto"
          )}>
            {/* Subtitle */}
            {subtitle && (
              <div className="inline-block">
                <span className="text-sm font-medium text-white bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                  {subtitle}
                </span>
              </div>
            )}
            
            {/* Main Title */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight">
              {title}
            </h1>
            
            {/* Description */}
            {description && (
              <p className="text-xl text-white/90 leading-relaxed">
                {description}
              </p>
            )}
            
            {/* CTA Button */}
            {ctaButton && (
              <div className="pt-4">
                <Button
                  asChild
                  variant={ctaButton.variant || "default"}
                  size="lg"
                  className="bg-white text-primary-900 hover:bg-white/90 px-8 py-3 text-base font-medium shadow-lg"
                >
                  <Link href={ctaButton.href}>
                    {ctaButton.text}
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Decorative gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-black/20 pointer-events-none" />
    </div>
  );
};
