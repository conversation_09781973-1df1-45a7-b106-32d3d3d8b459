"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { MapPin, Bed, Bath, Square } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { type PropertyShowcaseBannerProps } from "./types";

export const PropertyShowcaseBanner: React.FC<PropertyShowcaseBannerProps> = ({
  property,
  ctaButton,
  showFeatures = true,
  isActive,
}) => {
  const formatPrice = (price: number, unit: string) => {
    if (price >= 1000) {
      const kValue = price / 1000;
      return `$${kValue}k/${unit}`;
    }
    return `$${price}/${unit}`;
  };

  return (
    <div className="w-full h-full min-h-[400px] lg:min-h-[500px] bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center h-full py-8">
          {/* Property Image */}
          <div className="relative order-1">
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={property.images[0] || "/placeholder-property.jpg"}
                alt={property.title}
                fill
                className="object-cover transition-transform duration-700 hover:scale-105"
                priority={isActive}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
              />
              
              {/* Price Badge */}
              <div className="absolute top-4 left-4">
                <Badge className="bg-primary-700 text-white text-lg font-bold px-4 py-2">
                  {formatPrice(property.price, property.priceUnit)}
                </Badge>
              </div>
              
              {/* Image count indicator */}
              {property.images.length > 1 && (
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-black/50 text-white backdrop-blur-sm">
                    +{property.images.length - 1} more
                  </Badge>
                </div>
              )}
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-secondary-200 rounded-full opacity-20 blur-xl" />
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary-200 rounded-full opacity-20 blur-xl" />
          </div>

          {/* Property Details */}
          <div className="space-y-6 order-2">
            {/* Property Title */}
            <div className="space-y-3">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-primary-900 leading-tight">
                {property.title}
              </h1>
              
              {/* Address */}
              <div className="flex items-center gap-2 text-primary-600">
                <MapPin className="h-5 w-5" />
                <span className="text-lg">{property.address}</span>
              </div>
            </div>
            
            {/* Property Stats */}
            <div className="flex flex-wrap gap-6">
              {property.bedrooms && (
                <div className="flex items-center gap-2 text-primary-700">
                  <Bed className="h-5 w-5" />
                  <span className="font-medium">{property.bedrooms} Bed{property.bedrooms > 1 ? 's' : ''}</span>
                </div>
              )}
              
              {property.bathrooms && (
                <div className="flex items-center gap-2 text-primary-700">
                  <Bath className="h-5 w-5" />
                  <span className="font-medium">{property.bathrooms} Bath{property.bathrooms > 1 ? 's' : ''}</span>
                </div>
              )}
              
              {property.area && (
                <div className="flex items-center gap-2 text-primary-700">
                  <Square className="h-5 w-5" />
                  <span className="font-medium">{property.area} {property.areaUnit || 'sqft'}</span>
                </div>
              )}
            </div>
            
            {/* Features */}
            {showFeatures && property.features && property.features.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-primary-800">Key Features</h3>
                <div className="flex flex-wrap gap-2">
                  {property.features.slice(0, 4).map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-sm">
                      {feature}
                    </Badge>
                  ))}
                  {property.features.length > 4 && (
                    <Badge variant="outline" className="text-sm">
                      +{property.features.length - 4} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
            
            {/* CTA Button */}
            <div className="pt-4">
              <Button
                asChild
                variant={ctaButton.variant || "default"}
                size="lg"
                className="bg-primary-700 hover:bg-primary-800 text-white px-8 py-3 text-base font-medium"
              >
                <Link href={ctaButton.href}>
                  {ctaButton.text}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
