import { NAVBAR_HEIGHT } from "@/lib/constants";
import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";

export const Navbar = () => {
  return (
    <div
      style={{ "--navbar-height": `${NAVBAR_HEIGHT}px` } as React.CSSProperties}
      className={`fixed top-0 right-0 left-0 z-50 h-[var(--navbar-height)] shadow-xl`}
    >
      <div className="bg-primary-700 flex h-full w-full items-center justify-between px-4 sm:px-6 lg:px-8 py-3 text-white min-w-0">
        {/* Logo Section - Fixed width */}
        <div className="flex items-center gap-3 md:gap-4 flex-shrink-0 min-w-0">
          <Link
            href={"/"}
            className="hover:text-primary-300 cursor-pointer flex items-center gap-3"
            scroll={false}
          >
            <Image
              src={"/logo.svg"}
              alt="Rentiful Logo"
              width={24}
              height={24}
              className="h-6 w-6 flex-shrink-0"
            />
            <h1 className="text-lg sm:text-xl font-bold transition-all duration-300 whitespace-nowrap">
              RENT
              <span className="text-secondary-500 hover:text-secondary-300 font-light">
                IFUL
              </span>
            </h1>
          </Link>
        </div>

        {/* Center Text - Flexible width with overflow handling */}
        <div className="flex-1 px-4 lg:px-8 min-w-0 hidden lg:block">
          <p className="leading-7 text-center text-sm lg:text-base overflow-hidden text-ellipsis whitespace-nowrap">
            Discover your perfect rental apartment with our advanced search and filtering options.
          </p>
        </div>

        {/* Buttons Section - Fixed width */}
        <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
          <Button
            variant={"outline"}
            size="sm"
            className="bg-transparent text-white hover:bg-white/10 hover:text-white border-white/20 text-xs sm:text-sm px-2 sm:px-4"
          >
            <span className="hidden sm:inline">Sign In</span>
            <span className="sm:hidden">In</span>
          </Button>
          <Button
            variant={"outline"}
            size="sm"
            className="bg-secondary-700 hover:bg-secondary-800 text-white hover:text-white border-secondary-600 text-xs sm:text-sm px-2 sm:px-4"
          >
            <span className="hidden sm:inline">Sign Up</span>
            <span className="sm:hidden">Up</span>
          </Button>
        </div>
      </div>
    </div>
  );
};
