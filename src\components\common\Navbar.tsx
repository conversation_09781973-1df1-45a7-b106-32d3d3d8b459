import { NAVBAR_HEIGHT } from "@/lib/constants";
import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";

export const Navbar = () => {
  return (
    <div
      style={{ "--navbar-height": `${NAVBAR_HEIGHT}px` } as React.CSSProperties}
      className={`fixed top-0 right-0 left-0 z-50 h-[var(--navbar-height)] shadow-xl`}
    >
      <div className="bg-primary-700 flex h-full w-full items-center justify-between px-8 py-3 text-white">
        <div className="flex items-center gap-4 md:gap-6">
          <Link
            href={"/"}
            className="hover:text-primary-300 cursor-pointer"
            scroll={false}
          >
            <div className="flex items-center gap-3">
              <Image
                src={"/logo.svg"}
                alt="Rentiful Logo"
                width={24}
                height={24}
                className="h-6 w-6"
              />
              <h1 className="text-xl font-bold transition-all duration-300">
                RENT
                <span className="text-secondary-500 hover:text-secondary-300 font-light">
                  IFUL
                </span>
              </h1>
            </div>
          </Link>
        </div>
        <p className="leading-7 hidden md:block">
          Discover your perfect rental apartment with our advanced search and
          filtering options.
        </p>
        <div className="flex items-center gap-4">
          <Button
            variant={"outline"}
            className="bg-transparent text-white hover:bg-white/10 hover:text-white"
          >
            Sign In
          </Button>
          <Button
            variant={"outline"}
            className="bg-secondary-700 hover:bg-secondary-800 text-white hover:text-white"
          >
            Sign Up
          </Button>
        </div>
      </div>
    </div>
  );
};
